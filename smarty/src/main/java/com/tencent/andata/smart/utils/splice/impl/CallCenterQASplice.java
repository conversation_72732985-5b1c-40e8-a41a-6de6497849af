package com.tencent.andata.smart.utils.splice.impl;

import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.smart.enums.CallDirectionType;
import com.tencent.andata.smart.enums.OperationType;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.utils.StaffInfoUtils;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.control.Option;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

public class CallCenterQASplice extends TicketOperationRCSplice {

    private static final FlinkLog logger = FlinkLog.getInstance();

    /**
     * 根据场景类型处理不同的流水项
     *
     * @param item 流水项
     * @param strategy 策略
     * @return 拼接后的字符串
     */
    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        // 如果`data_type`为，则处理群消息流水
        JsonNode currItem = item.get().getCurrent();
        if (currItem.has("data_type") && currItem.get("data_type").asText().equals("cc_operation")) {
            return processCCItem(currItem);
        }

        return processTicketItem(currItem);
    }

    /**
     * 处理工单流水
     *
     * @param currItem 当前流水项
     * @return 拼接后的字符串
     */
    private String processTicketItem(JsonNode currItem) {

        OperationType operationType = getOperationType(currItem)
                .map(op -> OperationType.of(Integer.parseInt(op)))
                .get();

        String operateTime = currItem.get("operate_time").asText();
        String innerReply = cleanContent(currItem.get("inner_reply").asText());
        String staffName = StaffInfoUtils.getUserName(currItem.get("operator").asText());
        String nextStaffName = StaffInfoUtils.getUserName(currItem.get("next_operator").asText());

        switch (operationType) {
            case CREATE:
                return String.format("%s，坐席%s创建工单，在工单中记录客户问题的处理进展（工单日志）：\n%s", operateTime, staffName, innerReply);
            case TRANSFER:
                return String.format("%s，坐席%s将工单转移（转单）给坐席%s", operateTime, staffName, nextStaffName);
            case RETRIEVE:
                return String.format("%s，坐席%s进入工单处理客户问题", operateTime, staffName);
            case REPLY:
            case EFFECTIVE_CALL:
            case COMMENT:
                return String.format("%s，坐席%s在工单中记录客户问题的处理进展（工单日志）：\n%s", operateTime, staffName, innerReply);
            case CLOSE:
                return String.format("%s，坐席%s触发动作“结单”，并在工单中记录客户问题的处理进展（工单日志）：\n%s", operateTime, staffName, innerReply);
            default:
                return "";
        }
    }

    /**
     * 处理CallCenter录音流水
     *
     * @param currItem 当前流水项
     * @return 拼接后的字符串
     */
    private String processCCItem(JsonNode currItem) {
        CallDirectionType callDirection = CallDirectionType.fromCode(currItem.get("call_direction").asInt());
        String callTime = currItem.get("create_time").asText();
        String content = currItem.get("content").asText();
        String userId = currItem.get("user_id").asText();
        String callType = "";

        if (callDirection == CallDirectionType.INCOMING) {
            callType = "接听来自客户的电话";
        } else if (callDirection == CallDirectionType.OUTGOING) {
            callType = "通过电话外呼客户";
        }

        return String.format("%s，坐席%s %s，通话内容为：\n%s", callTime, userId, callType, content);
    }
}
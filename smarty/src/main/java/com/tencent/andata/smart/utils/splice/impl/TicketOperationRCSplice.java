package com.tencent.andata.smart.utils.splice.impl;

import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.utils.splice.base.AbstractItemSplice;
import com.tencent.andata.smart.enums.TicketOperationType;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.collection.HashMap;
import io.vavr.collection.Map;
import io.vavr.control.Option;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;

/**
 * 工单流水风控拼接实现
 */
public class TicketOperationRCSplice extends AbstractItemSplice {

    private static final String CUSTOMER_REPLY_TEMPLATE = "客户回复：%s\n";
    private static final String STAFF_REPLY_TEMPLATE = "坐席回复：%s\n";

    private static final Map<TicketOperationType, String> OPERATION_TEMPLATES = HashMap.of(
            // 客户回复
            TicketOperationType.CUSTOMER_REPLY, CUSTOMER_REPLY_TEMPLATE,
            TicketOperationType.CREATE, CUSTOMER_REPLY_TEMPLATE,
            TicketOperationType.URGE, CUSTOMER_REPLY_TEMPLATE,
            // 坐席回复
            TicketOperationType.WAIT_CUSTOMER_ADD_INFO, STAFF_REPLY_TEMPLATE,
            TicketOperationType.WAIT_CUSTOMER_CLOSE, STAFF_REPLY_TEMPLATE,
            TicketOperationType.REPLY, STAFF_REPLY_TEMPLATE

    );

    @Override
    protected String doSplice(Option<ElementContext<JsonNode>> item, Option<Strategy> strategy) {
        return item
                .filter(i -> strategy
                        .map(s -> s.trigger)
                        .map(t -> t.data[0])
                        .map(d -> d.get("service_channel"))
                        .map(JsonNode::asInt)
                        .getOrElse(0) != 27)
                .filter(i -> isBeforeTriggeredTimestamp(item, strategy))
                .map(ElementContext::getCurrent)
                .flatMap(this::extractReplyContent)
                .filter(content -> !isNullContent(content))
                .flatMap(content ->
                        getOperationType(item.get().getCurrent())
                                .map(TicketOperationType::fromCode)
                                .map(type -> formatReply(type, content))
                )
                .getOrNull();
    }

    protected Option<String> extractReplyContent(JsonNode node) {
        return Option.of(node)
                .map(n -> n.get("extern_reply"))
                .map(JsonNode::asText)
                .map(this::cleanContent);
    }

    private String formatReply(TicketOperationType type, String content) {
        return OPERATION_TEMPLATES
                .get(type)
                .map(template -> String.format(template, content))
                .getOrNull();
    }
}
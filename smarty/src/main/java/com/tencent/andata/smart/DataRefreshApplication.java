package com.tencent.andata.smart;

import com.tencent.andata.smart.access.config.AppConfig;
import com.tencent.andata.smart.access.pipeline.DataRetrospection;
import com.tencent.andata.utils.FlinkEnvUtils;
import com.tencent.andata.utils.FlinkEnvUtils.FlinkEnv;
import com.tencent.andata.utils.RainbowAppConfig;
import com.tencent.andata.utils.RainbowUtils;
import java.util.concurrent.TimeUnit;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DataRefreshApplication {

    private static final Logger log = LoggerFactory.getLogger(DataRefreshApplication.class);

    public static void main(String[] args) throws Exception {
        // 1. 环境初始化
        FlinkEnv fEnv = FlinkEnvUtils.getStreamTableEnv(args);
        StreamExecutionEnvironment env = fEnv.env();
        StreamTableEnvironment tEnv = fEnv.streamTEnv();

        // 2. 加载配置
        RainbowUtils rainbowUtils = RainbowAppConfig.getInstance();

        AppConfig appConfig = AppConfig
                .builder()
                .withRainbowUtils(rainbowUtils)
                .build();

        // 3. 创建数据源和处理流水线
        DataRetrospection pipeline = new DataRetrospection(fEnv, appConfig);
        pipeline.setupPipeline();

        // 4. 配置状态后端和检查点
        configureStateAndCheckpoint(env);

        // 5. 配置任务参数
        configureJobParameters(tEnv);

        // 6. 执行任务
        env.execute("quality-data-refresh");
    }

    private static void configureStateAndCheckpoint(StreamExecutionEnvironment env) {
        // 配置重启策略
        env.getConfig().enableObjectReuse();
        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                10, Time.of(1, TimeUnit.MINUTES), Time.of(10, TimeUnit.SECONDS)
        ));
    }

    private static void configureJobParameters(StreamTableEnvironment tEnv) {
        Configuration configuration = tEnv.getConfig().getConfiguration();

        configuration.setString("pipeline.task-name-length", "25");
        configuration.setString("metrics.latency.interval", "30000");
        configuration.setString("table.exec.state.ttl", "259200000");
        configuration.setString("state.savepoints.timeout", "600000");
        configuration.setString("table.exec.sink.upsert-materialize", "NONE");
        configuration.setString("table.optimizer.reuse-source-enabled", "true");
        configuration.setString("pipeline.name", "ingestion-layer-application");
        configuration.setString("state.backend.latency-track.keyed-state-enabled", "true");
    }
}
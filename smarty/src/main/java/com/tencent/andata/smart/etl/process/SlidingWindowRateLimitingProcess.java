package com.tencent.andata.smart.etl.process;

import com.tencent.andata.smart.strategy.model.Strategy;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

public class SlidingWindowRateLimitingProcess extends KeyedProcessFunction<String, Strategy, Strategy> {

    private final long rateLimit;
    private final long windowSize;
    private final long slideStep;
    private final int batchSize; // 每次定时器触发时输出的最大条数

    // 状态：计数窗口
    private transient ValueState<Long> countState;
    // 状态：窗口开始时间
    private transient ValueState<Long> windowStartState;
    // 状态：是否限流
    private transient ValueState<Boolean> isRateLimited;
    // 状态：缓存被限流的数据
    private transient ListState<byte[]> bufferedEvents;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public SlidingWindowRateLimitingProcess(long rateLimit, long windowSize, long slideStep, int batchSize) {
        this.rateLimit = rateLimit;
        this.windowSize = windowSize;
        this.slideStep = slideStep;
        this.batchSize = batchSize;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        countState = getRuntimeContext().getState(new ValueStateDescriptor<>("count", Long.class));
        windowStartState = getRuntimeContext().getState(new ValueStateDescriptor<>("windowStart", Long.class));
        isRateLimited = getRuntimeContext().getState(new ValueStateDescriptor<>("isRateLimited", Boolean.class));
        bufferedEvents = getRuntimeContext().getListState(new ListStateDescriptor<>("bufferedEvents", byte[].class));
    }

    @Override
    public void processElement(Strategy value, Context ctx, Collector<Strategy> out) throws Exception {
        long currentTime = ctx.timerService().currentProcessingTime();

        Long count = countState.value();
        Long windowStart = windowStartState.value();
        Boolean rateLimited = isRateLimited.value();

        if (windowStart == null || currentTime - windowStart >= windowSize) {
            // 初始化窗口
            windowStartState.update(currentTime);
            countState.update(1L);
            // 注册下一个滑动窗口的定时器
            ctx.timerService().registerProcessingTimeTimer(currentTime + slideStep);
        } else {
            // 更新计数
            countState.update(count == null ? 1L : count + 1);
        }

        if (rateLimited != null && rateLimited) {
            // 当前处于限流状态，将数据缓存
            byte[] compressValue = objectMapper.writeValueAsBytes(value);
            bufferedEvents.add(compress(compressValue));
            return;
        }

        if (count != null && count > rateLimit) {
            // 达到限流阈值，进入限流状态
            isRateLimited.update(true);
            byte[] compressValue = objectMapper.writeValueAsBytes(value);
            bufferedEvents.add(compress(compressValue)); // 当前数据也缓存
        } else {
            // 正常输出数据
            out.collect(value);
        }
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<Strategy> out) throws Exception {
        long currentTime = ctx.timerService().currentProcessingTime();

        // 检查并更新限流状态
        updateRateLimitStatus();

        // 输出缓存的数据并更新剩余事件
        List<byte[]> remainingEvents = outputBufferedEvents(out);

        // 如果仍有缓存数据，注册下一次定时器
        if (!remainingEvents.isEmpty()) {
            ctx.timerService().registerProcessingTimeTimer(currentTime + slideStep);
        }

        // 清理过期状态
        clearExpiredState(currentTime);
    }

    private void updateRateLimitStatus() throws Exception {
        if (isRateLimited.value() != null && isRateLimited.value()) {
            if (countState.value() == null || countState.value() <= rateLimit) {
                isRateLimited.update(false); // 解除限流
            }
        }
    }

    private List<byte[]> outputBufferedEvents(Collector<Strategy> out) throws Exception {
        Iterable<byte[]> buffered = bufferedEvents.get();
        List<byte[]> remainingEvents = new ArrayList<>();
        int outputCount = 0;

        for (byte[] event : buffered) {
            if (outputCount < batchSize) {
                out.collect(objectMapper.readValue(decompress(event), Strategy.class));
                outputCount++;
            } else {
                remainingEvents.add(event);
            }
        }

        bufferedEvents.update(remainingEvents);
        return remainingEvents;
    }

    private void clearExpiredState(long currentTime) throws Exception {
        if (countState.value() != null && currentTime - windowStartState.value() >= windowSize) {
            countState.clear();
        }
    }

    private byte[] compress(byte[] data) throws IOException {
        // 检查输入数据是否为空
        if (data == null || data.length == 0) {
            throw new IllegalArgumentException("Input data cannot be null or empty");
        }

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            gzipOutputStream.write(data);
            gzipOutputStream.finish(); // 确保所有数据都被写入
        }
        return byteArrayOutputStream.toByteArray(); // 返回压缩后的字节数组
    }

    private byte[] decompress(byte[] compressedData) throws IOException {
        // 检查输入数据是否为空
        if (compressedData == null || compressedData.length == 0) {
            throw new IllegalArgumentException("Input data cannot be null or empty");
        }

        try (ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(compressedData);
                GZIPInputStream gzipInputStream = new GZIPInputStream(byteArrayInputStream)) {
            // 使用 ByteArrayOutputStream 来收集解压缩后的数据
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;

            // 逐块读取解压缩的数据
            while ((bytesRead = gzipInputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }

            // 返回解压缩后的字节数组
            return byteArrayOutputStream.toByteArray();
        }
    }
}
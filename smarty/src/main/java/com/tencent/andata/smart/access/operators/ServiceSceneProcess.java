package com.tencent.andata.smart.access.operators;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.tencent.andata.log.FlinkLog;
import com.tencent.andata.utils.lookup.jdbc.HashMapJDBCLookupQuery;
import com.tencent.andata.utils.lookup.jdbc.JDBCSqlBuilderImpl;
import com.tencent.andata.utils.struct.DatabaseConf;
import com.tencent.andata.utils.struct.DatabaseEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import lombok.RequiredArgsConstructor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

@RequiredArgsConstructor
public class ServiceSceneProcess extends ProcessFunction<JsonNode, JsonNode> {

    private static final FlinkLog logger = FlinkLog.getInstance();
    private static final String TABLE_NAME = "dim_service_scenes";
    private static final List<String> SCENE_FIELDS = Collections.unmodifiableList(Arrays.asList(
            "service_scene_level1_name",
            "service_scene_level2_name",
            "service_scene_level3_name",
            "service_scene_level4_name"
    ));

    // 性能监控计数器
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong cacheHitCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private long lastLogTime = System.currentTimeMillis();

    private static final long LOG_INTERVAL_MS = 60000; // 1分钟

    private static final int CACHE_SIZE = 300000;
    private static final int CACHE_EXPIRE_MINUTES = 120;

    private final DatabaseConf dataWareConf;
    private HashMapJDBCLookupQuery serviceScenesQuery;

    private final Map<String, String> defaultSceneInfo;
    private Cache<Long, Map<String, String>> sceneCache;

    public ServiceSceneProcess(DatabaseConf dataWareConf) {
        this.dataWareConf = dataWareConf;
        this.defaultSceneInfo = Collections.unmodifiableMap(createDefaultSceneInfo());
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        initializeCache();
        initializeServiceScenesQuery();
        logger.info("[ServiceSceneProcess] initialized successfully");
    }

    private void initializeCache() {
        sceneCache = CacheBuilder.newBuilder()
                .maximumSize(CACHE_SIZE)
                .expireAfterWrite(CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES)
                .build();
    }

    @Override
    public void processElement(JsonNode input, Context context, Collector<JsonNode> collector) throws Exception {
        try {
            if (!validateInput(input)) {
                updateInputWithSceneInfo((ObjectNode) input, defaultSceneInfo);
                collector.collect(input);
                return;
            }

            enrichWithServiceScene(input);
            processedCount.incrementAndGet();
        } catch (Exception e) {
            logger.error(String.format("[ServiceSceneProcess] Error processing element: %s\n err_msg: %s", input, e));
            // 在错误情况下，仍然输出原始数据，但添加默认场景信息
            updateInputWithSceneInfo((ObjectNode) input, defaultSceneInfo);
            errorCount.incrementAndGet();
        }

        collector.collect(input);
        logMetrics();
    }

    private void initializeServiceScenesQuery() throws Exception {
        JDBCSqlBuilderImpl sqlBuilder = JDBCSqlBuilderImpl.builder()
                .tableName(TABLE_NAME)
                .selectField(SCENE_FIELDS)
                .conditionKeyList(Collections.singletonList("dim_id"))
                .limit(1);

        serviceScenesQuery = new HashMapJDBCLookupQuery(
                DatabaseEnum.PGSQL,
                dataWareConf,
                sqlBuilder
        );
        serviceScenesQuery.open();
    }

    private boolean validateInput(JsonNode jsonNode) {
        return jsonNode != null &&
                jsonNode.has("service_scene") &&
                !jsonNode.get("service_scene").isNull() &&
                (jsonNode.get("service_scene").isNumber() || jsonNode.get("service_scene").isTextual());
    }

    private Map<String, String> createDefaultSceneInfo() {
        Map<String, String> info = new HashMap<>(SCENE_FIELDS.size());
        SCENE_FIELDS.forEach(field -> info.put(field, ""));
        return info;
    }

    private void enrichWithServiceScene(JsonNode input) throws Exception {
        long sceneId = input.get("service_scene").asLong();
        Map<String, String> sceneInfo = getSceneInfo(sceneId);
        updateInputWithSceneInfo((ObjectNode) input, sceneInfo);
    }

    private Map<String, String> getSceneInfo(long sceneId) throws Exception {
        Map<String, String> cachedInfo = sceneCache.getIfPresent(sceneId);
        if (cachedInfo != null) {
            cacheHitCount.incrementAndGet();
            return cachedInfo;
        }

        Map<String, String> sceneInfo = queryServiceScene(sceneId);
        sceneCache.put(sceneId, sceneInfo);
        return sceneInfo;
    }

    private Map<String, String> queryServiceScene(long sceneId) throws Exception {
        HashMap<String, Object> params = new HashMap<>(1);
        params.put("dim_id", sceneId);

        List<HashMap<String, Object>> results = serviceScenesQuery.query(params);
        if (results.isEmpty()) {return defaultSceneInfo;}

        Map<String, String> sceneInfo = new HashMap<>(SCENE_FIELDS.size());
        HashMap<String, Object> result = results.get(0);
        SCENE_FIELDS.forEach(field ->
                sceneInfo.put(field, String.valueOf(result.getOrDefault(field, "")))
        );
        return Collections.unmodifiableMap(sceneInfo);
    }

    private void updateInputWithSceneInfo(ObjectNode input, Map<String, String> sceneInfo) {
        sceneInfo.forEach(input::put);
    }

    private void logMetrics() {
        long errorCnt = errorCount.get();
        long processCnt = processedCount.get();
        long cacheHitCnt = cacheHitCount.get();
        long currentTime = System.currentTimeMillis();

        if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
            lastLogTime = currentTime;
            logger.info(String.format("[ServiceSceneProcess] Processing metrics - Processed: "
                    + "%s, Errors: %s, Cache Hits: %s", processCnt, errorCnt, cacheHitCnt));
        }
    }

    @Override
    public void close() throws Exception {
        if (serviceScenesQuery != null) {
            try {
                serviceScenesQuery.close();
            } catch (Exception e) {
                logger.error("[ServiceSceneProcess] Error closing serviceScenesQuery " + e);
            }
        }
        if (sceneCache != null) {
            sceneCache.invalidateAll();
        }
    }
}
package splice;


import static com.tencent.andata.utils.IterableUtils.getElementsWithContext;

import com.tencent.andata.smart.config.AnalyzeConfig;
import com.tencent.andata.smart.config.ChunkConfig;
import com.tencent.andata.smart.strategy.analyze.Analyze;
import com.tencent.andata.smart.strategy.condition.Condition;
import com.tencent.andata.smart.enums.Scene;
import com.tencent.andata.smart.strategy.model.Strategy;
import com.tencent.andata.smart.strategy.trigger.*;
import com.tencent.andata.smart.utils.SpliceUtils;
import com.tencent.andata.utils.IterableUtils.ElementContext;
import io.vavr.control.Option;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.core.type.TypeReference;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.JsonNode;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;

public class SpliceUtilsTest {

    private SpliceUtils spliceUtils = new SpliceUtils();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static String REGEXPATTERN = "^\\d{2}-\\d{2} \\d{2}:\\d{2}";


    private List<JsonNode> item;

    private static List<JsonNode> getInputData() throws Exception {
        String filePath = "src/main/resources/operation.txt"; // 替换为你的文件路径
        String ticketOperationStr = new String(Files.readAllBytes(Paths.get(filePath)));
        // 将json转为List<JsonNode>
        return objectMapper.readValue(ticketOperationStr, new TypeReference<List<JsonNode>>() {});

    }

    /**
     * 生成上下文
     */
    private String buildContext(List<JsonNode> itemList, Strategy strategy) {
        return io.vavr.collection.List
                .ofAll(getElementsWithContext(itemList))
                .map(item -> buildItemContext(item, strategy))
                .filter(Option::isDefined)
                .map(Option::get)
                .foldLeft(
                        new StringBuilder(),
                        (sb, itemStr) -> appendWithSizeLimit(sb, itemStr, strategy.chunk.maxSize)
                ).toString().trim();
    }

    /**
     * 在不超过最大大小的情况下追加字符串
     */
    private static StringBuilder appendWithSizeLimit(StringBuilder sb, String str, long maxSize) {
        return Option.when(
                !sb.toString().contains(str.replaceAll(REGEXPATTERN, "")) && sb.length() + str.length() <= maxSize,
                () -> sb.append(str)
        ).getOrElse(sb);
    }

    /**
     * 处理单个item的上下文
     */
    private Option<String> buildItemContext(ElementContext<JsonNode> item, Strategy strategy) {
        return Option.of(spliceUtils.execute(item, strategy));
    }

    private static String getInputData(String dataType) {
        return String.format("{\n" +
                "    \"action_type\": \"insert\",\n" +
                "    \"record_update_time\": \"2025-04-12 22:40:10\",\n" +
                "    \"requestid\": \"08ec4883-17ac-11f0-8abd-5254006e3911\",\n" +
                "    \"rpc_name\": \"AutoFinishConversation\",\n" +
                "    \"table\": \"ods_im_online_customer_service_backend_data\",\n" +
                "    \"value_of_primary_key\": \"1744468810047467554|@TGS#247SBMPQN\",\n" +
                "    \"conversation_id\": \"@TGS#247SBMPQN\",\n" +
                "    \"owner_uin\": \"144115225284242073\",\n" +
                "    \"uin\": \"144115225284242073\",\n" +
                "    \"uid\": \"\",\n" +
                "    \"source\": \"MA\",\n" +
                "    \"status\": 12,\n" +
                "    \"category_id\": 1416,\n" +
                "    \"first_should_assign\": 5669,\n" +
                "    \"should_assign\": 866,\n" +
                "    \"fact_assign\": 866,\n" +
                "    \"service_scene\": 263523,\n" +
                "    \"current_staff\": \"82495\",\n" +
                "    \"staffs\": \"82349,77522,49919,1325,82495,30264\",\n" +
                "    \"appraise\": \"没有解决问题，麻烦尽快解决！已经影响到了工作！\",\n" +
                "    \"service_rate\": 1,\n" +
                "    \"unsatisfy_reason\": 0,\n" +
                "    \"conversation_service_rate\": 0,\n" +
                "    \"conversation_unsatisfy_reason\": \"3\",\n" +
                "    \"product_rate\": 0,\n" +
                "    \"product_unsatisfy_reason\": \"\",\n" +
                "    \"recommend_score\": -1,\n" +
                "    \"appraise_time\": 1743873620,\n" +
                "    \"create_time\": 1743740415,\n" +
                "    \"customer_updated_time\": 1744198472,\n" +
                "    \"staff_updated_time\": 1744209477,\n" +
                "    \"ticket_ids\": \"\",\n" +
                "    \"conversation_ticket_ids\": \"14206389\",\n" +
                "    \"title\": \"提供不了，我也不可能现场提供啊！微信视频通话有声音，但是会议中对方听不到我的声音\",\n" +
                "    \"all_level_category\": \"{\\\"first_level\\\":{\\\"id\\\":517,\\\"name\\\":\\\"其他腾讯云产品\\\",\\\"name_en\\\":\\\"Other service\\\",\\\"service_scene\\\":0},\\\"second_level\\\":{\\\"id\\\":992," +
                "\\\"name\\\":\\\"腾讯会议\\\",\\\"name_en\\\":\\\"tencent meeting\\\",\\\"service_scene\\\":0},\\\"third_level\\\":{\\\"id\\\":1416,\\\"name\\\":\\\"音视频相关\\\",\\\"name_en\\\":\\\"Audio and video related\\\"," +
                "\\\"service_scene\\\":304247}}\",\n" +
                "    \"customer_first_updated_time\": 1743740494,\n" +
                "    \"staff_first_updated_time\": 1743740420,\n" +
                "    \"is_alarm\": false,\n" +
                "    \"solve_status\": 0,\n" +
                "    \"customer_name\": \"雪靜吶\uE14C\",\n" +
                "    \"is_clean\": 1,\n" +
                "    \"customer_type\": 0,\n" +
                "    \"finish_time\": 1744468810,\n" +
                "    \"staff_title\": \"您好很荣幸为您服务&nbsp;您的问题我们已经收到了&nbsp;这里先看下您的问题&nbsp;&nbsp;有结果第一时间反馈您\",\n" +
                "    \"is_transferred\": false,\n" +
                "    \"chat_type\": 0,\n" +
                "    \"company_id\": 0,\n" +
                "    \"is_ticket_created\": true,\n" +
                "    \"set_wait_status_time\": 0,\n" +
                "    \"apply_finish_time\": 1744209598,\n" +
                "    \"awaiting_supplement_time\": 0,\n" +
                "    \"creator\": \"144115225284242073\",\n" +
                "    \"creator_type\": 1,\n" +
                "    \"finisher\": \"system\",\n" +
                "    \"finish_type\": 3,\n" +
                "    \"post\": 3,\n" +
                "    \"parent\": \"\",\n" +
                "    \"contact\": \"bYiu-amX7RHh2qukrjZz5alGkYLQcPAeN-7uniF-BzApHbG6RS7bxUyspBWPok3u5w==\",\n" +
                "    \"marked_by_staffs\": \"\",\n" +
                "    \"is_ola_alarm\": false,\n" +
                "    \"has_complaint\": false,\n" +
                "    \"sem_category_id\": 0,\n" +
                "    \"service_scene_level4_name\": \"麦克风驱动/兼容问题\",\n" +
                "    \"service_scene_level2_name\": \"腾讯会议SaaS免费版\",\n" +
                "    \"service_scene_level1_name\": \"腾讯会议产品部/产品运营中心\",\n" +
                "    \"service_scene_level3_name\": \"音频问题\",\n" +
                "    \"data_type\": \"webim\",\n" +
                "    \"display_content\": \"\"\n" +
                "  }", dataType);
    }

    private static Strategy buildTestStrategy(String chunkType, String dataType) throws Exception {
        Strategy strategy = Strategy.builder()
                .id(1)
                .sceneIdentify("@TGS#247SBMPQN")
                .name("WebIM结单质检")
                .scene(Scene.WebIM)
                .condition(
                        Condition.builder()
                                .expression("data_type == 'webim'")
                                .build()
                )
                .trigger(Trigger.builder()
                        .type(TriggerType.Immediately)
                        .needPersist(true)
                        .build())
                .chunk(ChunkConfig.WebIMReply)
                .analyzes(Collections.singletonList(AnalyzeConfig.Professional_Skills).toArray(new Analyze[0]))
                .build();

        strategy.trigger.setTriggerTimestamp(1746780431000L);

        strategy.trigger.data = new JsonNode[]{objectMapper.readTree(getInputData(dataType))};

        return strategy;
    }

    @Before
    public void setUp() throws Exception {
        item = getInputData();
    }

    @Test
    public void testExecute() throws Exception {
        Strategy strategy = buildTestStrategy("Conversation", "webim");
        // assert !buildContext(item, strategy).trim().isEmpty();
        System.out.println(buildContext(item, strategy));
    }
}